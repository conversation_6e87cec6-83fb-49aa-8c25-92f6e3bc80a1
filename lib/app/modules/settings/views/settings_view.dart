import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pocket_trac/colors.dart';
import 'package:pocket_trac/extension.dart';
import '../controllers/settings_controller.dart';

class SettingsView extends GetView<SettingsController> {
  const SettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: controller.obx(
        (state) => Column(
          children: [
            // Custom header with gradient background
            _buildHeader(context),
            // Scrollable content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: _buildMainContent().toList(),
                ),
              ),
            ),
          ],
        ),
        onLoading: const Center(
          child: CircularProgressIndicator(),
        ),
        onError: (error) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '載入失敗',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              Text(error.toString()),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => controller.onRefresh(),
                child: const Text('重試'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [ErpColors.gradientStart, ErpColors.gradientEnd],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: _buildHeaderContent().toList(),
          ),
        ),
      ),
    );
  }

  Iterable<Widget> _buildHeaderContent() sync* {
    // User info card
    yield Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.1)),
      ),
      child: Row(
        children: _buildUserInfoRowContent().toList(),
      ),
    );
  }

  Iterable<Widget> _buildUserInfoRowContent() sync* {
    // User avatar
    yield Container(
      width: 64,
      height: 64,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(32),
      ),
      child: const Icon(
        Icons.person,
        color: Colors.white,
        size: 32,
      ),
    );

    yield const SizedBox(width: 12);

    // User info
    yield const Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '張小明',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 4),
          Text(
            '<EMAIL>',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
          SizedBox(height: 2),
          Text(
            '已同步：2024/03/15 14:30',
            style: TextStyle(
              color: Colors.white60,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );

    yield IconButton(
      onPressed: () {
        // Edit profile functionality
      },
      icon: const Icon(
        Icons.edit,
        color: Colors.white70,
        size: 20,
      ),
    );
  }

  Iterable<Widget> _buildMainContent() sync* {
    yield _buildDataManagementSection();
    yield const SizedBox(height: 24);
    yield _buildAppSettingsSection();
    yield const SizedBox(height: 24);
    yield _buildSecurityPrivacySection();
    yield const SizedBox(height: 24);
    yield _buildAboutSection();
    yield const SizedBox(height: 24);
    yield _buildLogoutButton();
    yield const SizedBox(height: 16);
    yield _buildCopyright();
    yield const SizedBox(height: 80); // Space for bottom navigation
  }

  Widget _buildDataManagementSection() {
    return _buildSection(
      title: '資料管理',
      items: _buildDataManagementItems().toList(),
    );
  }

  Iterable<Widget> _buildDataManagementItems() sync* {
    yield _buildSettingItem(
      icon: Icons.cloud_sync,
      iconColor: Colors.blue,
      title: '雲端同步',
      subtitle: '自動備份至雲端',
      trailing: _buildToggleSwitch(true),
    );

    yield _buildSettingItem(
      icon: Icons.backup,
      iconColor: Colors.green,
      title: '匯出資料',
      subtitle: '匯出至 CSV 或 PDF',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Export data functionality
      },
    );

    yield _buildSettingItem(
      icon: Icons.file_download,
      iconColor: Colors.purple,
      title: '匯入資料',
      subtitle: '從其他應用程式匯入',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Import data functionality
      },
    );

    yield _buildSettingItem(
      icon: Icons.delete_forever,
      iconColor: Colors.red,
      title: '清除資料',
      subtitle: '刪除所有本地資料',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Clear data functionality
      },
    );
  }

  Widget _buildAppSettingsSection() {
    return _buildSection(
      title: 'App 設定',
      items: _buildAppSettingsItems().toList(),
    );
  }

  Iterable<Widget> _buildAppSettingsItems() sync* {
    yield _buildSettingItem(
      icon: Icons.language,
      iconColor: Colors.grey,
      title: '語言設定',
      subtitle: '繁體中文',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Language settings
      },
    );

    yield _buildSettingItem(
      icon: Icons.notifications,
      iconColor: Colors.orange,
      title: '通知設定',
      subtitle: '管理推播通知',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Notification settings
      },
    );

    yield Obx(() => _buildSettingItem(
          icon: Icons.palette,
          iconColor: Colors.green,
          title: '主題設定',
          subtitle: controller.prefProvider.currentThemeMode.display,
          trailing: const Icon(Icons.chevron_right, color: Colors.grey),
          onTap: showThemeDialog,
        ));

    yield _buildSettingItem(
      icon: Icons.currency_exchange,
      iconColor: Colors.blue,
      title: '幣別設定',
      subtitle: 'TWD 新台幣',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Currency settings
      },
    );
  }

  Widget _buildSecurityPrivacySection() {
    return _buildSection(
      title: '安全與隱私',
      items: _buildSecurityPrivacyItems().toList(),
    );
  }

  Iterable<Widget> _buildSecurityPrivacyItems() sync* {
    yield _buildSettingItem(
      icon: Icons.fingerprint,
      iconColor: Colors.blue,
      title: 'Touch ID / Face ID',
      subtitle: '使用生物辨識解鎖',
      trailing: _buildToggleSwitch(true),
    );

    yield _buildSettingItem(
      icon: Icons.lock,
      iconColor: Colors.orange,
      title: '密碼設定',
      subtitle: '變更應用程式密碼',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Password settings
      },
    );

    yield _buildSettingItem(
      icon: Icons.privacy_tip,
      iconColor: Colors.purple,
      title: '隱私權設定',
      subtitle: '管理個人資料使用',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Privacy settings
      },
    );
  }

  Widget _buildAboutSection() {
    return _buildSection(
      title: '關於',
      items: _buildAboutItems().toList(),
    );
  }

  Iterable<Widget> _buildAboutItems() sync* {
    yield _buildSettingItem(
      icon: Icons.info,
      iconColor: Colors.blue,
      title: '版本資訊',
      subtitle: 'v1.0.0 (Build 1)',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Version info
      },
    );

    yield _buildSettingItem(
      icon: Icons.help,
      iconColor: Colors.green,
      title: '使用說明',
      subtitle: '查看操作指南',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Help guide
      },
    );

    yield _buildSettingItem(
      icon: Icons.feedback,
      iconColor: Colors.orange,
      title: '意見回饋',
      subtitle: '回報問題或建議',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Feedback
      },
    );

    yield _buildSettingItem(
      icon: Icons.gavel,
      iconColor: Colors.purple,
      title: '服務條款',
      subtitle: '使用條款與政策',
      trailing: const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: () {
        // Terms of service
      },
    );
  }

  Widget _buildSection({required String title, required List<Widget> items}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF374151),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: _addDividers(items),
          ),
        ),
      ],
    );
  }

  List<Widget> _addDividers(List<Widget> items) {
    List<Widget> itemsWithDividers = [];
    for (int i = 0; i < items.length; i++) {
      itemsWithDividers.add(items[i]);
      if (i < items.length - 1) {
        itemsWithDividers.add(
          const Divider(
            height: 1,
            thickness: 1,
            color: Color(0xFFF3F4F6),
            indent: 68, // Space for icon + padding
          ),
        );
      }
    }
    return itemsWithDividers;
  }

  Widget _buildSettingItem({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required Widget trailing,
    VoidCallback? onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Icon container
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: iconColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  icon,
                  color: iconColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              // Text content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF111827),
                      ),
                    ),
                    Text(
                      subtitle,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ),
              // Trailing widget
              trailing,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildToggleSwitch(bool value) {
    return Switch(
      value: value,
      onChanged: (newValue) {
        // Handle toggle change
      },
      activeColor: ErpColors.primary,
      inactiveThumbColor: Colors.grey[300],
      inactiveTrackColor: Colors.grey[200],
    );
  }

  Widget _buildLogoutButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          // Logout functionality
          _showLogoutDialog();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red[500],
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: const Text(
          '登出帳號',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildCopyright() {
    return const Column(
      children: [
        Text(
          'PocketTrac © 2024',
          style: TextStyle(
            fontSize: 12,
            color: Color(0xFF9CA3AF),
          ),
        ),
        SizedBox(height: 4),
        Text(
          '服務條款 • 隱私權政策',
          style: TextStyle(
            fontSize: 12,
            color: Color(0xFF9CA3AF),
          ),
        ),
      ],
    );
  }

  void _showLogoutDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('確認登出'),
        content: const Text('您確定要登出帳號嗎？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              // Perform logout
            },
            child: const Text(
              '登出',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }


  /// 显示主题选择对话框
  Future<void> showThemeDialog() async {
    Get.dialog(
      AlertDialog(
        title: const Text('選擇主題'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: ThemeMode.values.map((mode) {
            return Obx(() => RadioListTile(
                  title: Text(mode.display),
                  subtitle: Text(_getThemeModeDescription(mode)),
                  value: mode,
                  groupValue: controller.prefProvider.currentThemeMode,
                  onChanged: (value) {
                    if (value != null) {
                      controller.prefProvider.themeMode = value;
                      Get.back();
                    }
                  },
                ));
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  /// 获取主题模式描述
  String _getThemeModeDescription(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return '始終使用淺色主題';
      case ThemeMode.dark:
        return '始終使用深色主題';
      case ThemeMode.system:
        return '根據系統設定自動切換';
    }
  }
}
